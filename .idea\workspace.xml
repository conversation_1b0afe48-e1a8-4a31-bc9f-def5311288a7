<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="2b6c2b98-5526-4a98-a4a2-d413dff66adc" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="31ak63WzIwjMfsrks4reI4rsrJR" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.411" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="2b6c2b98-5526-4a98-a4a2-d413dff66adc" name="更改" comment="" />
      <created>1755769393212</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755769393212</updated>
      <workItem from="1755769394553" duration="1000" />
    </task>
    <servers />
  </component>
</project>