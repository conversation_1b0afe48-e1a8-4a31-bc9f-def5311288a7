(function(e){function t(t){for(var n,o,a=t[0],i=t[1],p=t[2],d=0,l=[];d<a.length;d++)o=a[d],Object.prototype.hasOwnProperty.call(c,o)&&c[o]&&l.push(c[o][0]),c[o]=0;for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n]);u&&u(t);while(l.length)l.shift()();return s.push.apply(s,p||[]),r()}function r(){for(var e,t=0;t<s.length;t++){for(var r=s[t],n=!0,a=1;a<r.length;a++){var i=r[a];0!==c[i]&&(n=!1)}n&&(s.splice(t--,1),e=o(o.s=r[0]))}return e}var n={},c={app:0},s=[];function o(t){if(n[t])return n[t].exports;var r=n[t]={i:t,l:!1,exports:{}};return e[t].call(r.exports,r,r.exports,o),r.l=!0,r.exports}o.m=e,o.c=n,o.d=function(e,t,r){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},o.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(o.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)o.d(r,n,function(t){return e[t]}.bind(null,n));return r},o.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="/";var a=window["webpackJsonp"]=window["webpackJsonp"]||[],i=a.push.bind(a);a.push=t,a=a.slice();for(var p=0;p<a.length;p++)t(a[p]);var u=i;s.push([0,"chunk-vendors"]),r()})({0:function(e,t,r){e.exports=r("56d7")},"0428":function(e,t,r){},1:function(e,t){},2:function(e,t){},3:function(e,t){},"4b9b":function(e,t,r){"use strict";r("fa97")},"56d7":function(e,t,r){"use strict";r.r(t);var n=r("2b0e"),c=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("router-view")],1)},s=[],o=r("2877"),a={},i=Object(o["a"])(a,c,s,!1,null,null,null),p=i.exports,u=r("8c4f"),d=function(){var e=this,t=e._self._c;return t("div",{staticClass:"home"},[t("div",{staticClass:"navigation"},[t("router-link",{staticClass:"nav-button",attrs:{to:"/page1"}},[t("div",{staticClass:"button-content"},[t("h3",[e._v("页面1")])])]),t("router-link",{staticClass:"nav-button",attrs:{to:"/page2"}},[t("div",{staticClass:"button-content"},[t("h3",[e._v("页面2")])])]),t("router-link",{staticClass:"nav-button",attrs:{to:"/page3"}},[t("div",{staticClass:"button-content"},[t("h3",[e._v("页面3")])])])],1)])},l=[],m={name:"Home"},g=m,y=(r("a003"),Object(o["a"])(g,d,l,!1,null,"657d6354",null)),D=y.exports,E=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],staticClass:"price-screen-container"},[t("div",{staticClass:"table-section"},[e._m(0),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-wrapper"},[t("div",{ref:"scrollingTable",staticClass:"scrolling-table"},[t("el-table",{staticClass:"price-table",attrs:{data:e.displayData,stripe:"","show-header":!0,height:"100%"}},[t("el-table-column",{attrs:{prop:"code",label:"编码",width:"120",align:"center"}}),t("el-table-column",{attrs:{prop:"name",label:"名称",width:"200",align:"center"}}),t("el-table-column",{attrs:{prop:"spec",label:"规格",width:"150",align:"center"}}),t("el-table-column",{attrs:{prop:"unit",label:"单位",width:"80",align:"center"}}),t("el-table-column",{attrs:{prop:"type",label:"类型",width:"120",align:"center"}}),t("el-table-column",{attrs:{prop:"price",label:"单价(元)",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",{staticClass:"price-text"},[e._v("¥"+e._s(r.row.price))])]}}])})],1)],1)])]),t("div",{staticClass:"video-section"},[t("div",{staticClass:"video-wrapper"},[t("video",{ref:"priceVideo",staticClass:"price-video",attrs:{src:e.videoSrc,autoplay:"",loop:"",muted:""},domProps:{muted:!0},on:{error:e.handleVideoError,loadstart:e.handleVideoLoadStart,canplay:e.handleVideoCanPlay}},[e._v(" 您的浏览器不支持视频播放 ")]),e.videoError?t("div",{staticClass:"video-error"},[t("i",{staticClass:"el-icon-video-camera-solid"}),t("p",[e._v("视频加载失败")])]):e._e()])])])},h=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-header"},[t("h2",[e._v("商品价格信息")])])}],M=[{code:"MED001",name:"阿莫西林胶囊",spec:"0.25g*24粒",unit:"盒",type:"处方药",price:"15.80"},{code:"MED002",name:"布洛芬缓释胶囊",spec:"0.3g*20粒",unit:"盒",type:"处方药",price:"28.50"},{code:"MED003",name:"维生素C片",spec:"0.1g*100片",unit:"瓶",type:"非处方药",price:"12.30"},{code:"MED004",name:"感冒灵颗粒",spec:"10g*9袋",unit:"盒",type:"非处方药",price:"18.90"},{code:"MED005",name:"头孢克肟胶囊",spec:"0.1g*12粒",unit:"盒",type:"处方药",price:"45.60"},{code:"MED006",name:"复方甘草片",spec:"50片",unit:"瓶",type:"处方药",price:"8.70"},{code:"MED007",name:"氯雷他定片",spec:"10mg*7片",unit:"盒",type:"处方药",price:"22.40"},{code:"MED008",name:"蒙脱石散",spec:"3g*10袋",unit:"盒",type:"非处方药",price:"16.80"},{code:"MED009",name:"奥美拉唑肠溶胶囊",spec:"20mg*14粒",unit:"盒",type:"处方药",price:"35.20"},{code:"MED010",name:"板蓝根颗粒",spec:"10g*20袋",unit:"盒",type:"非处方药",price:"13.50"},{code:"MED011",name:"罗红霉素胶囊",spec:"150mg*12粒",unit:"盒",type:"处方药",price:"26.70"},{code:"MED012",name:"双氯芬酸钠缓释片",spec:"75mg*10片",unit:"盒",type:"处方药",price:"19.80"},{code:"MED013",name:"复合维生素B片",spec:"100片",unit:"瓶",type:"非处方药",price:"14.60"},{code:"MED014",name:"硝苯地平缓释片",spec:"20mg*30片",unit:"盒",type:"处方药",price:"42.30"},{code:"MED015",name:"藿香正气水",spec:"10ml*10支",unit:"盒",type:"非处方药",price:"11.90"},{code:"MED016",name:"左氧氟沙星片",spec:"0.1g*10片",unit:"盒",type:"处方药",price:"38.40"},{code:"MED017",name:"咳特灵胶囊",spec:"0.5g*24粒",unit:"盒",type:"非处方药",price:"21.70"},{code:"MED018",name:"甲硝唑片",spec:"0.2g*100片",unit:"瓶",type:"处方药",price:"9.80"},{code:"MED019",name:"健胃消食片",spec:"0.8g*36片",unit:"盒",type:"非处方药",price:"17.20"},{code:"MED020",name:"阿司匹林肠溶片",spec:"25mg*30片",unit:"盒",type:"处方药",price:"6.50"},{code:"MED021",name:"盐酸二甲双胍片",spec:"0.25g*48片",unit:"盒",type:"处方药",price:"32.80"},{code:"MED022",name:"硫酸沙丁胺醇气雾剂",spec:"100μg*200揿",unit:"支",type:"处方药",price:"24.60"},{code:"MED023",name:"复方氨酚烷胺片",spec:"12片",unit:"盒",type:"非处方药",price:"9.50"},{code:"MED024",name:"马来酸氯苯那敏片",spec:"4mg*100片",unit:"瓶",type:"处方药",price:"7.20"},{code:"MED025",name:"盐酸雷尼替丁胶囊",spec:"150mg*24粒",unit:"盒",type:"处方药",price:"18.90"},{code:"MED026",name:"复方丹参滴丸",spec:"270丸",unit:"瓶",type:"非处方药",price:"29.80"},{code:"MED027",name:"硝酸甘油片",spec:"0.5mg*100片",unit:"瓶",type:"处方药",price:"12.40"},{code:"MED028",name:"盐酸氨溴索口服溶液",spec:"100ml",unit:"瓶",type:"非处方药",price:"16.70"},{code:"MED029",name:"复方甲氧那明胶囊",spec:"24粒",unit:"盒",type:"处方药",price:"33.50"},{code:"MED030",name:"葡萄糖酸钙口服溶液",spec:"10ml*10支",unit:"盒",type:"非处方药",price:"8.90"},{code:"MED031",name:"盐酸左氧氟沙星胶囊",spec:"0.1g*6粒",unit:"盒",type:"处方药",price:"25.60"},{code:"MED032",name:"复方感冒灵片",spec:"12片",unit:"盒",type:"非处方药",price:"11.30"},{code:"MED033",name:"硫酸亚铁片",spec:"0.3g*100片",unit:"瓶",type:"非处方药",price:"6.80"},{code:"MED034",name:"盐酸西替利嗪片",spec:"10mg*12片",unit:"盒",type:"处方药",price:"19.40"},{code:"MED035",name:"复方桔梗片",spec:"24片",unit:"盒",type:"非处方药",price:"13.70"},{code:"MED036",name:"硝苯地平片",spec:"10mg*100片",unit:"瓶",type:"处方药",price:"15.20"},{code:"MED037",name:"盐酸多西环素片",spec:"0.1g*12片",unit:"盒",type:"处方药",price:"22.90"},{code:"MED038",name:"复方氨基酸胶囊",spec:"0.25g*36粒",unit:"盒",type:"非处方药",price:"28.40"},{code:"MED039",name:"马来酸依那普利片",spec:"10mg*28片",unit:"盒",type:"处方药",price:"36.80"},{code:"MED040",name:"复方维生素片",spec:"60片",unit:"瓶",type:"非处方药",price:"18.50"},{code:"MED041",name:"盐酸普萘洛尔片",spec:"10mg*100片",unit:"瓶",type:"处方药",price:"9.60"},{code:"MED042",name:"复方新诺明片",spec:"12片",unit:"盒",type:"处方药",price:"7.30"},{code:"MED043",name:"硫酸庆大霉素片",spec:"40mg*12片",unit:"盒",type:"处方药",price:"14.80"},{code:"MED044",name:"复方氨酚咖敏片",spec:"12片",unit:"盒",type:"非处方药",price:"10.90"},{code:"MED045",name:"盐酸氯丙嗪片",spec:"25mg*100片",unit:"瓶",type:"处方药",price:"12.70"},{code:"MED046",name:"复方鱼腥草片",spec:"36片",unit:"盒",type:"非处方药",price:"16.20"},{code:"MED047",name:"硝酸异山梨酯片",spec:"5mg*100片",unit:"瓶",type:"处方药",price:"11.40"},{code:"MED048",name:"盐酸氨基葡萄糖胶囊",spec:"0.24g*20粒",unit:"盒",type:"非处方药",price:"45.80"},{code:"MED049",name:"复方黄连素片",spec:"24片",unit:"盒",type:"非处方药",price:"8.60"},{code:"MED050",name:"马来酸曲美布汀片",spec:"0.1g*30片",unit:"盒",type:"处方药",price:"27.30"},{code:"MED051",name:"盐酸氟西汀胶囊",spec:"20mg*28粒",unit:"盒",type:"处方药",price:"52.40"},{code:"MED052",name:"复方板蓝根颗粒",spec:"15g*9袋",unit:"盒",type:"非处方药",price:"19.80"},{code:"MED053",name:"硫酸阿托品片",spec:"0.3mg*100片",unit:"瓶",type:"处方药",price:"8.90"},{code:"MED054",name:"盐酸美托洛尔片",spec:"25mg*20片",unit:"盒",type:"处方药",price:"15.60"},{code:"MED055",name:"复方穿心莲片",spec:"24片",unit:"盒",type:"非处方药",price:"12.40"},{code:"MED056",name:"硝酸毛果芸香碱滴眼液",spec:"5ml",unit:"支",type:"处方药",price:"23.70"},{code:"MED057",name:"盐酸地尔硫卓片",spec:"30mg*24片",unit:"盒",type:"处方药",price:"18.90"},{code:"MED058",name:"复方甘草酸苷片",spec:"25mg*100片",unit:"瓶",type:"处方药",price:"34.50"},{code:"MED059",name:"马来酸氯苯那敏注射液",spec:"1ml*10支",unit:"盒",type:"处方药",price:"16.80"},{code:"MED060",name:"复方氨酚那敏片",spec:"12片",unit:"盒",type:"非处方药",price:"9.70"},{code:"MED061",name:"盐酸克林霉素胶囊",spec:"150mg*12粒",unit:"盒",type:"处方药",price:"21.30"},{code:"MED062",name:"硫酸特布他林片",spec:"2.5mg*20片",unit:"盒",type:"处方药",price:"14.20"},{code:"MED063",name:"复方丹参片",spec:"60片",unit:"瓶",type:"非处方药",price:"17.60"},{code:"MED064",name:"盐酸吗啉胍片",spec:"0.1g*100片",unit:"瓶",type:"处方药",price:"11.80"},{code:"MED065",name:"复方氨酚愈敏口服溶液",spec:"120ml",unit:"瓶",type:"非处方药",price:"22.90"},{code:"MED066",name:"硝酸咪康唑栓",spec:"0.2g*7粒",unit:"盒",type:"处方药",price:"26.40"},{code:"MED067",name:"盐酸异丙嗪片",spec:"25mg*100片",unit:"瓶",type:"处方药",price:"13.50"},{code:"MED068",name:"复方氨基比林注射液",spec:"2ml*10支",unit:"盒",type:"处方药",price:"19.70"},{code:"MED069",name:"马来酸噻吗洛尔滴眼液",spec:"5ml",unit:"支",type:"处方药",price:"31.20"},{code:"MED070",name:"复方薄荷脑滴鼻液",spec:"10ml",unit:"瓶",type:"非处方药",price:"7.80"},{code:"MED071",name:"盐酸利多卡因注射液",spec:"5ml*10支",unit:"盒",type:"处方药",price:"15.40"},{code:"MED072",name:"硫酸新霉素片",spec:"0.25g*12片",unit:"盒",type:"处方药",price:"10.60"},{code:"MED073",name:"复方氨酚烷胺颗粒",spec:"6g*9袋",unit:"盒",type:"非处方药",price:"14.30"},{code:"MED074",name:"盐酸苯海拉明片",spec:"25mg*100片",unit:"瓶",type:"处方药",price:"8.70"},{code:"MED075",name:"复方磺胺甲噁唑片",spec:"0.48g*100片",unit:"瓶",type:"处方药",price:"12.90"},{code:"MED076",name:"硝酸甘油气雾剂",spec:"4.9g",unit:"瓶",type:"处方药",price:"28.60"},{code:"MED077",name:"盐酸氯胺酮注射液",spec:"2ml*10支",unit:"盒",type:"处方药",price:"45.80"},{code:"MED078",name:"复方愈创木酚磺酸钾口服溶液",spec:"100ml",unit:"瓶",type:"非处方药",price:"18.50"},{code:"MED079",name:"马来酸桂哌齐特片",spec:"80mg*20片",unit:"盒",type:"处方药",price:"24.70"},{code:"MED080",name:"复方氨酚咖敏胶囊",spec:"12粒",unit:"盒",type:"非处方药",price:"11.20"},{code:"MED081",name:"盐酸丁卡因胶浆",spec:"20g",unit:"支",type:"处方药",price:"16.90"},{code:"MED082",name:"硫酸阿米卡星注射液",spec:"2ml*10支",unit:"盒",type:"处方药",price:"32.40"},{code:"MED083",name:"复方氨酚那敏颗粒",spec:"6g*12袋",unit:"盒",type:"非处方药",price:"13.80"},{code:"MED084",name:"盐酸哌替啶片",spec:"25mg*100片",unit:"瓶",type:"处方药",price:"29.50"},{code:"MED085",name:"复方甘草合剂",spec:"100ml",unit:"瓶",type:"非处方药",price:"9.40"},{code:"MED086",name:"硝酸益康唑栓",spec:"0.2g*6粒",unit:"盒",type:"处方药",price:"22.80"},{code:"MED087",name:"盐酸氯丙嗪注射液",spec:"2ml*10支",unit:"盒",type:"处方药",price:"17.60"},{code:"MED088",name:"复方氨酚愈敏片",spec:"12片",unit:"盒",type:"非处方药",price:"10.30"},{code:"MED089",name:"马来酸氯苯那敏缓释片",spec:"8mg*10片",unit:"盒",type:"处方药",price:"15.70"},{code:"MED090",name:"复方薄荷脑鼻用喷雾剂",spec:"20ml",unit:"瓶",type:"非处方药",price:"12.60"},{code:"MED091",name:"盐酸普鲁卡因注射液",spec:"2ml*10支",unit:"盒",type:"处方药",price:"8.90"},{code:"MED092",name:"硫酸链霉素注射液",spec:"1g*10支",unit:"盒",type:"处方药",price:"25.40"},{code:"MED093",name:"复方氨酚烷胺口服溶液",spec:"60ml",unit:"瓶",type:"非处方药",price:"16.80"},{code:"MED094",name:"盐酸赛庚啶片",spec:"2mg*100片",unit:"瓶",type:"处方药",price:"11.50"},{code:"MED095",name:"复方磺胺嘧啶片",spec:"0.5g*100片",unit:"瓶",type:"处方药",price:"14.20"},{code:"MED096",name:"硝酸甘油舌下片",spec:"0.5mg*100片",unit:"瓶",type:"处方药",price:"9.80"},{code:"MED097",name:"盐酸氯胺酮片",spec:"50mg*20片",unit:"盒",type:"处方药",price:"38.60"},{code:"MED098",name:"复方愈创甘油醚口服溶液",spec:"100ml",unit:"瓶",type:"非处方药",price:"19.30"},{code:"MED099",name:"马来酸氯苯那敏糖浆",spec:"100ml",unit:"瓶",type:"处方药",price:"13.70"},{code:"MED100",name:"复方氨酚咖敏口服溶液",spec:"100ml",unit:"瓶",type:"非处方药",price:"15.90"}],f={name:"priceScreen",data(){return{loading:!0,videoError:!1,videoSrc:r("8639"),scrollTimer:null,scrollSpeed:2,currentScrollTop:0,priceData:M}},computed:{displayData(){return[...this.priceData,...this.priceData]}},created(){this.testFun()},mounted(){this.initPage()},methods:{async testFun(){const e=await this.$api.price.queryPrice();e.success?console.log("queryPrice查",e):(console.log("queryPrice查2",e),this.$message.error(e.message))},async initPage(){setTimeout(()=>{this.loading=!1,this.$nextTick(()=>{this.startScrolling()})},1e3)},startScrolling(){const e=this.$refs.scrollingTable;e&&this.$nextTick(()=>{const t=()=>{this.currentScrollTop+=this.scrollSpeed;const r=e.scrollHeight,n=r/2;this.currentScrollTop>=n&&(this.currentScrollTop=0),e.scrollTop=this.currentScrollTop,this.scrollTimer=requestAnimationFrame(t)};this.scrollTimer=requestAnimationFrame(t)})},clearScrollTimer(){this.scrollTimer&&(cancelAnimationFrame(this.scrollTimer),this.scrollTimer=null)},handleVideoError(){this.videoError=!0,console.error("视频加载失败")},handleVideoLoadStart(){this.videoError=!1},handleVideoCanPlay(){this.videoError=!1}},beforeDestroy(){this.clearScrollTimer()}},v=f,C=(r("e655"),Object(o["a"])(v,E,h,!1,null,"3954d98f",null)),S=C.exports,b=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-container"},[t("h1",[e._v("页面2")]),t("p",[e._v("这是页面2的内容，用于展示大屏数据可视化内容。")]),e._m(0),t("router-link",{staticClass:"back-btn",attrs:{to:"/"}},[e._v("返回首页")])],1)},T=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"content-area"},[t("p",[e._v("在这里可以添加图表、数据展示等内容")])])}],_={name:"Page2",data(){return{}}},w=_,O=(r("4b9b"),Object(o["a"])(w,b,T,!1,null,"53ad22e1",null)),x=O.exports,$=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-container"},[t("h1",[e._v("页面3")]),t("p",[e._v("这是页面3的内容，用于展示大屏数据可视化内容。")]),e._m(0),t("router-link",{staticClass:"back-btn",attrs:{to:"/"}},[e._v("返回首页")])],1)},P=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"content-area"},[t("p",[e._v("在这里可以添加图表、数据展示等内容")])])}],R={name:"Page3",data(){return{}}},A=R,I=(r("9cab"),Object(o["a"])(A,$,P,!1,null,"26e90a32",null)),U=I.exports;n["default"].use(u["a"]);const q=[{path:"/",name:"Home",component:D},{path:"/page1",name:"Page1",component:S},{path:"/page2",name:"Page2",component:x},{path:"/page3",name:"Page3",component:U}],H=new u["a"]({mode:"history",base:"/",routes:q});var j=H,Y=r("8750"),F=(r("f5df"),r("b20f"),r("5c96")),k=r.n(F),B=(r("0fae"),r("cee4"));const N="/api",V={development:{baseURL:N+"/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS",timeout:3e4,rejectUnauthorized:!1},production:{baseURL:N+"/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS",timeout:15e3,rejectUnauthorized:!1}},L="production",Q=V[L],G={headers:{"Content-Type":"text/xml; charset=utf-8",SOAPAction:"http://www.dhcc.com.cn/DHC.Published.ServiceForHATM.BS.ServiceForHATM.HIPMessageServer"},namespace:"http://www.dhcc.com.cn",envelope:{soap:"http://schemas.xmlsoap.org/soap/envelope/"}},z={QUERY_DEPARTMENT:"MES0018",QUERY_DOCTOR:"MES0038",QUERY_SCHEDULE:"MES0039",QUERY_TIME_INFO:"MES0040",QUERY_PRICE:"MES0061"};const J=B["a"].create({baseURL:Q.baseURL,timeout:Q.timeout,headers:G.headers,httpsAgent:new(r("24f8").Agent)({rejectUnauthorized:Q.rejectUnauthorized})});function W(e,t){return`<?xml version="1.0" encoding="utf-8"?>\n<soap:Envelope xmlns:soap="${G.envelope.soap}">\n    <soap:Body>\n        <HIPMessageServer xmlns="${G.namespace}">\n            <action>${e}</action>\n            <message>\n                <![CDATA[${t}]]>\n            </message>\n        </HIPMessageServer>\n    </soap:Body>\n</soap:Envelope>`}function K(e){try{const r=e.match(/<!\[CDATA\[(.*?)\]\]>/s);if(r&&r[1]){const e=r[1].trim();if(e.startsWith("<"))return X(e);try{return JSON.parse(e)}catch(t){return{data:e}}}return{rawResponse:e}}catch(r){return console.error("解析SOAP响应失败:",r),{error:"响应解析失败",rawResponse:e}}}function X(e){const t={},r=/<(\w+)>([^<]*)<\/\1>/g;let n;while(null!==(n=r.exec(e))){const[,e,r]=n;t[e]=r.trim()}return Object.keys(t).length>0?t:{data:e}}async function Z(e,t,r={}){try{console.log("发送SOAP请求 - Action: "+e),console.log("请求消息:",t);const n=W(e,t),c=await J.post("",n,{...r,headers:{...G.headers,...r.headers}});console.log("SOAP响应状态:",c.status),console.log("SOAP响应数据:",c.data);const s=K(c.data);return{success:!0,data:s,status:c.status,headers:c.headers}}catch(n){return console.error("SOAP请求失败:",n),n.response?{success:!1,error:"服务器响应错误",status:n.response.status,data:n.response.data,message:n.message}:n.request?{success:!1,error:"网络连接错误",message:"无法连接到服务器，请检查网络连接"}:{success:!1,error:"请求配置错误",message:n.message}}}J.interceptors.request.use(e=>(console.log("发送SOAP请求:",e.url),e),e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),J.interceptors.response.use(e=>(console.log("收到SOAP响应:",e.status),e),e=>(console.error("响应拦截器错误:",e),Promise.reject(e)));var ee=J;async function te(e={}){try{const{hospitalId:t="SGSDYRMYY",extUserId:r="NOVA001",extOrgCode:n="",clientType:c="",departmentType:s="",departmentCode:o="",departmentGroupCode:a=""}=e,i=`<Request>\n  <TradeCode>1012</TradeCode>\n  <ExtOrgCode>${n}</ExtOrgCode>\n  <ClientType>${c}</ClientType>\n  <HospitalId>${t}</HospitalId>\n  <DepartmentType>${s}</DepartmentType>\n  <DepartmentCode>${o}</DepartmentCode>\n  <DepartmentGroupCode>${a}</DepartmentGroupCode>\n  <ExtUserID>${r}</ExtUserID>\n</Request>`;console.log("科室查询请求参数:",e);const p=await Z(z.QUERY_DEPARTMENT,i);return p.success?(console.log("科室查询成功:",p.data),{success:!0,data:p.data,message:"科室查询成功"}):(console.error("科室查询失败:",p.error),{success:!1,error:p.error,message:p.message||"科室查询失败"})}catch(t){return console.error("科室查询异常:",t),{success:!1,error:"查询异常",message:t.message||"科室查询过程中发生异常"}}}async function re(){return await te()}async function ne(e){return await te({departmentType:e})}async function ce(e){return await te({departmentGroupCode:e})}var se={queryDepartment:te,getAllDepartments:re,queryDepartmentByType:ne,querySubDepartments:ce};async function oe(e={}){try{const{hospitalId:t="SGSDYRMYY",extUserId:r="NOVA001",departmentCode:n,extOrgCode:c="",clientType:s="",doctorCode:o=""}=e;if(!n)return{success:!1,error:"参数错误",message:"科室代码不能为空"};const a=`<Request>\n  <TradeCode>1013</TradeCode>\n  <ExtOrgCode>${c}</ExtOrgCode>\n  <ClientType>${s}</ClientType>\n  <HospitalId>${t}</HospitalId>\n  <ExtUserID>${r}</ExtUserID>\n  <DepartmentCode>${n}</DepartmentCode>\n  <DoctorCode>${o}</DoctorCode>\n</Request>`;console.log("医生查询请求参数:",e);const i=await Z(z.QUERY_DOCTOR,a);return i.success?(console.log("医生查询成功:",i.data),{success:!0,data:i.data,message:"医生查询成功"}):(console.error("医生查询失败:",i.error),{success:!1,error:i.error,message:i.message||"医生查询失败"})}catch(t){return console.error("医生查询异常:",t),{success:!1,error:"查询异常",message:t.message||"医生查询过程中发生异常"}}}async function ae(e){return await oe({departmentCode:e})}async function ie(e,t){return await oe({departmentCode:e,doctorCode:t})}var pe={queryDoctor:oe,queryDoctorsByDepartment:ae,queryDoctorInfo:ie};async function ue(e={}){try{const{hospitalId:t="SGSDYRMYY",extUserId:r="NOVA001",startDate:n,endDate:c,departmentCode:s,doctorCode:o="",stopScheduleFlag:a="N",rbasSessionCode:i=""}=e;if(!n||!c||!s)return{success:!1,error:"参数错误",message:"开始日期、结束日期和科室代码不能为空"};const p=`<Request>\n  <HospitalId>${t}</HospitalId>\n  <ExtUserID>${r}</ExtUserID>\n  <StartDate>${n}</StartDate>\n  <EndDate>${c}</EndDate>\n  <DepartmentCode>${s}</DepartmentCode>\n  <DoctorCode>${o}</DoctorCode>\n  <StopScheduleFlag>${a}</StopScheduleFlag>\n  <RBASSessionCode>${i}</RBASSessionCode>\n  <TradeCode>1004</TradeCode>\n</Request>`;console.log("排班查询请求参数:",e);const u=await Z(z.QUERY_SCHEDULE,p);return u.success?(console.log("排班查询成功:",u.data),{success:!0,data:u.data,message:"排班查询成功"}):(console.error("排班查询失败:",u.error),{success:!1,error:u.error,message:u.message||"排班查询失败"})}catch(t){return console.error("排班查询异常:",t),{success:!1,error:"查询异常",message:t.message||"排班查询过程中发生异常"}}}async function de(e={}){try{const{hospitalId:t="SGSDYRMYY",extUserId:r="NOVA001",departmentCode:n,doctorCode:c,serviceDate:s,rbasSessionCode:o="",scheduleItemCode:a=""}=e;if(!n||!c||!s)return{success:!1,error:"参数错误",message:"科室代码、医生代码和出诊日期不能为空"};const i=`<Request>\n  <TradeCode>10041</TradeCode>\n  <HospitalId>${t}</HospitalId>\n  <ExtUserID>${r}</ExtUserID>\n  <DepartmentCode>${n}</DepartmentCode>\n  <DoctorCode>${c}</DoctorCode>\n  <RBASSessionCode>${o}</RBASSessionCode>\n  <ScheduleItemCode>${a}</ScheduleItemCode>\n  <ServiceDate>${s}</ServiceDate>\n</Request>`;console.log("分时信息查询请求参数:",e);const p=await Z(z.QUERY_TIME_INFO,i);return p.success?(console.log("分时信息查询成功:",p.data),{success:!0,data:p.data,message:"分时信息查询成功"}):(console.error("分时信息查询失败:",p.error),{success:!1,error:p.error,message:p.message||"分时信息查询失败"})}catch(t){return console.error("分时信息查询异常:",t),{success:!1,error:"查询异常",message:t.message||"分时信息查询过程中发生异常"}}}async function le(e,t=""){const r=(new Date).toISOString().split("T")[0];return await ue({startDate:r,endDate:r,departmentCode:e,doctorCode:t})}async function me(e,t,r=""){return await ue({startDate:e,endDate:e,departmentCode:t,doctorCode:r})}var ge={querySchedule:ue,queryScheduleTimeInfo:de,queryTodaySchedule:le,queryScheduleByDate:me};async function ye(e={}){try{const{alias:t="MR",tradeCode:r="9100"}=e;if(!t)return{success:!1,error:"参数错误",message:"收费项目别名不能为空"};const n=`<Request><Alias>${t}</Alias><TradeCode>${r}</TradeCode></Request>`;console.log("物价查询请求参数:",e);const c=await Z("MES0061",n);return c.success?(console.log("物价查询成功:",c.data),{success:!0,data:c.data,message:"物价查询成功"}):(console.error("物价查询失败:",c.error),{success:!1,error:c.error,message:c.message||"物价查询失败"})}catch(t){return console.error("物价查询异常:",t),{success:!1,error:"查询异常",message:t.message||"物价查询过程中发生异常"}}}var De={queryPrice:ye};const Ee={soap:{client:ee,sendRequest:Z},department:se,doctor:pe,schedule:ge,price:De};n["default"].config.productionTip=!1,n["default"].prototype.$api=Ee,n["default"].use(k.a),new n["default"]({router:j,render:e=>e(p)}).$mount("#app"),Y["a"].init({dh:1080,dw:1920,el:"#app",resize:!0})},8639:function(e,t,r){e.exports=r.p+"static/media/price_screen_video.2f327022.mp4"},"9cab":function(e,t,r){"use strict";r("c8ee")},a003:function(e,t,r){"use strict";r("eec6")},b20f:function(e,t,r){},c8ee:function(e,t,r){},e655:function(e,t,r){"use strict";r("0428")},eec6:function(e,t,r){},fa97:function(e,t,r){}});