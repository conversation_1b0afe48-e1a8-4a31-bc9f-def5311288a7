{"_from": "@types/node@*", "_id": "@types/node@24.3.0", "_inBundle": false, "_integrity": "sha512-aPTXCrfwnDLj4VvXrm+UUCQjNEvJgNA8s5F1cvwQU+3KNltTOkBm1j30uNLyqqPNe7gE3KFzImYoZEfLhp4Yow==", "_location": "/@types/node", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/node@*", "name": "@types/node", "escapedName": "@types%2fnode", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/http-proxy"], "_resolved": "https://registry.npmmirror.com/@types/node/-/node-24.3.0.tgz", "_shasum": "89b09f45cb9a8ee69466f18ee5864e4c3eb84dec", "_spec": "@types/node@*", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\@types\\http-proxy", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "Microsoft TypeScript", "url": "https://github.com/Microsoft"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno"}, {"name": "<PERSON>", "url": "https://github.com/r3nya"}, {"name": "<PERSON>", "url": "https://github.com/btoueg"}, {"name": "<PERSON>", "url": "https://github.com/touffy"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mohsen1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/galkin"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/chyzwar"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/trivikr"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/yoursunny"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/qwelias"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON>", "url": "https://github.com/victorperin"}, {"name": "NodeJS Contributors", "url": "https://github.com/NodeJS"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU"}, {"name": "wafuwafu13", "url": "https://github.com/wafuwafu13"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/Semigradsky"}, {"name": "<PERSON>", "url": "https://github.com/Renegade334"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anonrig"}], "dependencies": {"undici-types": "~7.10.0"}, "deprecated": false, "description": "TypeScript definitions for node", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node", "license": "MIT", "main": "", "name": "@types/node", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "typeScriptVersion": "5.2", "types": "index.d.ts", "typesPublisherContentHash": "1db0510763ba3afd8e54c0591e60a100a7b90926f5d7da28ae32d8f845d725da", "typesVersions": {"<=5.6": {"*": ["ts5.6/*"]}, "<=5.7": {"*": ["ts5.7/*"]}}, "version": "24.3.0"}