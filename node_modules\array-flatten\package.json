{"_from": "array-flatten@1.1.1", "_id": "array-flatten@1.1.1", "_inBundle": false, "_integrity": "sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==", "_location": "/array-flatten", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "array-flatten@1.1.1", "name": "array-flatten", "escapedName": "array-flatten", "rawSpec": "1.1.1", "saveSpec": null, "fetchSpec": "1.1.1"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmmirror.com/array-flatten/-/array-flatten-1.1.1.tgz", "_shasum": "9a5f699051b1e7073328f2a008968b64ea2955d2", "_spec": "array-flatten@1.1.1", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "bugs": {"url": "https://github.com/blakeembrey/array-flatten/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Flatten an array of nested arrays into a single flat array", "devDependencies": {"istanbul": "^0.3.13", "mocha": "^2.2.4", "pre-commit": "^1.0.7", "standard": "^3.7.3"}, "files": ["array-flatten.js", "LICENSE"], "homepage": "https://github.com/blakeembrey/array-flatten", "keywords": ["array", "flatten", "arguments", "depth"], "license": "MIT", "main": "array-flatten.js", "name": "array-flatten", "repository": {"type": "git", "url": "git://github.com/blakeembrey/array-flatten.git"}, "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "version": "1.1.1"}