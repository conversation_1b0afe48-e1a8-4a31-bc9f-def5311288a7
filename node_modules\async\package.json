{"_from": "async@^3.2.6", "_id": "async@3.2.6", "_inBundle": false, "_integrity": "sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==", "_location": "/async", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "async@^3.2.6", "name": "async", "escapedName": "async", "rawSpec": "^3.2.6", "saveSpec": null, "fetchSpec": "^3.2.6"}, "_requiredBy": ["/portfinder"], "_resolved": "https://registry.npmmirror.com/async/-/async-3.2.6.tgz", "_shasum": "1b0728e14929d51b85b449b7f06e27c1145e38ce", "_spec": "async@^3.2.6", "_where": "C:\\Users\\<USER>\\Desktop\\新建文件夹\\test\\node_modules\\portfinder", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/caolan/async/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Higher-order functions and common patterns for asynchronous code", "devDependencies": {"@babel/core": "7.25.2", "@babel/eslint-parser": "^7.16.5", "babel-minify": "^0.5.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-istanbul": "^7.0.0", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-preset-es2015": "^6.3.13", "babel-preset-es2017": "^6.22.0", "babel-register": "^6.26.0", "babelify": "^10.0.0", "benchmark": "^2.1.1", "bluebird": "^3.4.6", "browserify": "^17.0.0", "chai": "^4.2.0", "cheerio": "^0.22.0", "es6-promise": "^4.2.8", "eslint": "^8.6.0", "eslint-plugin-prefer-arrow": "^1.2.3", "fs-extra": "^11.1.1", "jsdoc": "^4.0.3", "karma": "^6.3.12", "karma-browserify": "^8.1.0", "karma-firefox-launcher": "^2.1.2", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.0", "karma-safari-launcher": "^1.0.0", "mocha": "^6.1.4", "native-promise-only": "^0.8.0-a", "nyc": "^17.0.0", "rollup": "^4.2.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-npm": "^2.0.0", "rsvp": "^4.8.5", "semver": "^7.3.5", "yargs": "^17.3.1"}, "homepage": "https://caolan.github.io/async/", "keywords": ["async", "callback", "module", "utility"], "license": "MIT", "main": "dist/async.js", "module": "dist/async.mjs", "name": "async", "nyc": {"exclude": ["test"]}, "repository": {"type": "git", "url": "git+https://github.com/caolan/async.git"}, "scripts": {"coverage": "nyc npm run mocha-node-test -- --grep @nycinvalid --invert", "jsdoc": "jsdoc -c ./support/jsdoc/jsdoc.json && node support/jsdoc/jsdoc-fix-html.js", "lint": "eslint --fix .", "mocha-browser-test": "karma start", "mocha-node-test": "mocha", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "test": "npm run lint && npm run mocha-node-test"}, "version": "3.2.6"}